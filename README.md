# 高级日出帧动画项目

一个基于HTML5 Canvas和JavaScript实现的高级日出帧动画项目，支持程序生成和实际图片帧两种模式，通过滚动驱动创造流畅的视觉体验。

## 🌅 项目特点

- **双模式支持**: 程序生成帧 + 实际图片帧
- **滚动驱动**: 将页面滚动与动画进度精确绑定
- **智能回退**: 图片加载失败时自动切换到程序生成模式
- **响应式设计**: 适配桌面、平板、手机多种设备
- **性能优化**: 预生成帧、内存管理、渲染优化
- **交互控制**: 自动播放、手动控制、进度显示

## 📁 文件结构

```
滚动动画/
├── sunrise-frame-animation.html      # 原始程序生成版本
├── sunrise-image-frames.html         # 图片帧支持版本
├── frame-processor.js                # 图片处理工具
├── package.json                      # 项目配置
├── README.md                         # 项目说明
├── 技术文档-日出帧动画.md             # 详细技术文档
├── 图片帧替换指南.md                  # 图片替换指南
└── frames/                           # 图片帧目录
    ├── desktop/                      # 桌面版图片
    ├── tablet/                       # 平板版图片
    └── mobile/                       # 手机版图片
```

## 🚀 快速开始

### 1. 基础使用

直接在浏览器中打开HTML文件：

```bash
# 程序生成版本
open sunrise-frame-animation.html

# 图片帧版本
open sunrise-image-frames.html
```

### 2. 本地服务器运行

为了避免跨域问题，建议使用本地服务器：

```bash
# 使用Python
python3 -m http.server 8000

# 使用Node.js
npm install
npm run serve-node

# 然后访问 http://localhost:8000
```

### 3. 图片帧处理

如果您有自己的动画帧图片，可以使用处理工具：

```bash
# 安装ImageMagick (macOS)
brew install imagemagick

# 处理图片帧
node frame-processor.js --input ./your-frames --output ./frames --count 120

# 生成测试帧
npm run generate-test
```

## 🎮 使用说明

### 基本操作

1. **滚动控制**: 滚动页面查看动画进度
2. **自动播放**: 点击"自动播放"按钮独立播放动画
3. **重置动画**: 点击"重置动画"回到页面顶部
4. **模式切换**: 在图片帧版本中可以切换显示模式

### 控制面板

- **帧数**: 显示总帧数
- **当前帧**: 显示当前播放的帧号
- **滚动进度**: 显示滚动百分比
- **加载状态**: 显示当前使用的模式

## 🛠️ 技术实现

### 核心技术

- **HTML5 Canvas**: 图形渲染和动画绘制
- **JavaScript ES6+**: 动画逻辑和交互控制
- **CSS3**: 样式设计和响应式布局
- **requestAnimationFrame**: 性能优化的动画循环

### 关键算法

1. **帧生成算法**: 程序化生成120帧日出动画
2. **滚动映射**: 将滚动位置映射到动画帧
3. **图片加载**: 异步加载和缓存管理
4. **性能优化**: 节流、预加载、内存管理

### 动画阶段

- **深夜** (0-20%): 深蓝色调，星星可见
- **黎明** (20-40%): 天空开始变亮
- **日出** (40-60%): 橙红色调，太阳出现
- **上午** (60-80%): 过渡到天蓝色
- **白天** (80-100%): 明亮的蓝天

## 📱 响应式支持

项目支持多种设备尺寸：

- **桌面**: 1920x1080 及以上
- **平板**: 1024x768
- **手机**: 750x1334

图片帧会根据设备自动选择合适的尺寸版本。

## ⚡ 性能优化

### 帧预生成
- 初始化时生成所有帧
- 使用离屏Canvas渲染
- 避免滚动时的实时计算

### 内存管理
- 合理控制帧数量
- LRU缓存策略
- 及时释放不需要的资源

### 渲染优化
- requestAnimationFrame同步刷新率
- 滚动事件节流
- Canvas尺寸动态适配

## 🔧 自定义配置

### 修改帧数

```javascript
const sunriseAnimation = new SunriseFrameAnimation({
    frameCount: 60,  // 修改为60帧
    useImages: true
});
```

### 自定义图片路径

```javascript
const sunriseAnimation = new SunriseFrameAnimation({
    useImages: true,
    basePath: 'assets/frames/',
    fileExtension: '.png'
});
```

### 调整动画速度

```javascript
// 修改自动播放间隔
this.autoPlayInterval = setInterval(() => {
    // ...
}, 100);  // 改为100ms间隔
```

## 🎨 图片帧制作建议

### 图片规格
- **格式**: JPG (复杂场景) 或 PNG (需要透明度)
- **尺寸**: 建议1920x1080或更高
- **质量**: JPG质量80-90%
- **命名**: frame_001.jpg 到 frame_120.jpg

### 制作流程
1. 在动画软件中创建日出动画
2. 导出为图片序列
3. 使用frame-processor.js处理图片
4. 测试加载和播放效果

## 🐛 故障排除

### 图片加载失败
- 检查图片路径是否正确
- 确认图片文件存在
- 检查浏览器控制台错误信息
- 系统会自动回退到程序生成模式

### 性能问题
- 减少帧数量
- 降低图片质量
- 使用更小的图片尺寸
- 检查内存使用情况

### 兼容性问题
- 确保浏览器支持HTML5 Canvas
- 检查JavaScript ES6+支持
- 在移动设备上测试触摸交互

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- GitHub Issues: [项目Issues页面]
- Email: [<EMAIL>]

---

**享受创建美丽动画的过程！** 🌅✨
