<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日出图片帧动画 - 滚动驱动</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            overflow-x: hidden;
            background: #000;
        }

        .scroll-container {
            height: 600vh;
            position: relative;
        }

        .canvas-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            z-index: 1;
        }

        #sunriseCanvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 1.2rem;
            z-index: 3;
            text-align: center;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
        }

        .loading.hidden {
            display: none;
        }

        .progress-bar {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            z-index: 3;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b35, #ffd700, #87ceeb);
            border-radius: 3px;
            width: 0%;
            transition: width 0.2s ease-out;
        }

        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 3;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            color: white;
            font-size: 14px;
            min-width: 200px;
        }

        .controls button {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px 0;
            display: block;
            width: 100%;
        }

        .controls button:hover {
            background: #ff4500;
        }

        .controls button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .mode-selector {
            margin-bottom: 10px;
        }

        .mode-selector select {
            width: 100%;
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ccc;
        }

        .content {
            position: relative;
            z-index: 2;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin-top: 100vh;
            padding: 50px;
            min-height: 200vh;
            color: white;
        }

        .error-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 4;
            text-align: center;
            display: none;
        }

        @media (max-width: 768px) {
            .controls {
                top: 10px;
                right: 10px;
                padding: 10px;
                min-width: 150px;
            }
            
            .progress-bar {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="scroll-container">
        <div class="canvas-container">
            <canvas id="sunriseCanvas"></canvas>
        </div>

        <div class="loading" id="loading">
            <div id="loadingText">准备加载动画帧...</div>
            <div style="margin-top: 10px; font-size: 0.9rem;">请稍候</div>
        </div>

        <div class="error-message" id="errorMessage">
            <div>加载失败，已切换到程序生成模式</div>
            <button onclick="hideError()">确定</button>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="controls">
            <div class="mode-selector">
                <label>动画模式:</label>
                <select id="modeSelector" onchange="switchMode()">
                    <option value="images">图片帧模式</option>
                    <option value="generated">程序生成模式</option>
                </select>
            </div>
            <div>帧数: <span id="frameCount">0</span></div>
            <div>当前帧: <span id="currentFrame">0</span></div>
            <div>滚动进度: <span id="scrollProgress">0%</span></div>
            <div>加载状态: <span id="loadStatus">准备中</span></div>
            <button onclick="resetAnimation()">重置动画</button>
            <button onclick="toggleAutoPlay()" id="autoPlayBtn">自动播放</button>
            <button onclick="preloadFrames()" id="preloadBtn">预加载帧</button>
        </div>

        <div class="content">
            <h1>高级日出图片帧动画</h1>
            <p>这个版本支持使用实际的图片帧来创建更精美的动画效果。系统会自动检测图片是否可用，如果加载失败会回退到程序生成模式。</p>
            
            <h2>功能特点</h2>
            <ul style="margin: 20px 0; line-height: 1.8;">
                <li>🖼️ 支持实际图片帧动画</li>
                <li>🔄 智能回退机制</li>
                <li>📱 响应式图片加载</li>
                <li>⚡ 内存优化管理</li>
                <li>🎮 灵活的控制选项</li>
            </ul>
            
            <h2>使用说明</h2>
            <p>将您的动画帧图片放在 <code>frames/</code> 目录下，命名为 <code>frame_001.jpg</code> 到 <code>frame_120.jpg</code>。系统会自动加载这些图片并创建流畅的滚动动画。</p>
        </div>
    </div>

    <script>
        // 图片帧加载器
        class ImageFrameLoader {
            constructor(config) {
                this.frameCount = config.frameCount || 120;
                this.basePath = config.basePath || 'frames/';
                this.fileExtension = config.fileExtension || '.jpg';
                this.images = [];
                this.loadedCount = 0;
                this.onProgress = config.onProgress || (() => {});
                this.onComplete = config.onComplete || (() => {});
                this.onError = config.onError || (() => {});
            }

            async loadAllFrames() {
                const promises = [];
                
                for (let i = 1; i <= this.frameCount; i++) {
                    const frameNumber = i.toString().padStart(3, '0');
                    const imagePath = `${this.basePath}frame_${frameNumber}${this.fileExtension}`;
                    console.log(imagePath,'88888888');
                    promises.push(this.loadSingleFrame(imagePath, i - 1));
                }

                try {
                    await Promise.allSettled(promises);
                    
                    // 检查是否有足够的帧加载成功
                    const successfulFrames = this.images.filter(img => img !== null).length;
                    if (successfulFrames < this.frameCount * 0.5) {
                        throw new Error(`Only ${successfulFrames} frames loaded successfully`);
                    }
                    
                    this.onComplete(this.images);
                } catch (error) {
                    this.onError(error);
                }
            }

            loadSingleFrame(imagePath, index) {
                return new Promise((resolve) => {
                    const img = new Image();
                    
                    img.onload = () => {
                        this.images[index] = img;
                        this.loadedCount++;
                        this.onProgress(this.loadedCount, this.frameCount);
                        resolve(img);
                    };
                    
                    img.onerror = () => {
                        console.warn(`Failed to load frame: ${imagePath}`);
                        this.images[index] = null; // 标记为失败
                        resolve(null);
                    };
                    
                    img.src = imagePath;
                });
            }
        }

        // 主动画类
        class SunriseFrameAnimation {
            constructor(config = {}) {
                this.canvas = document.getElementById('sunriseCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.frames = [];
                this.totalFrames = config.frameCount || 120;
                this.currentFrameIndex = 0;
                this.isAutoPlaying = false;
                this.autoPlayInterval = null;
                this.useImages = config.useImages !== false; // 默认尝试使用图片
                this.config = config;
                
                this.setupCanvas();
                this.initializeAnimation();
                this.bindEvents();
            }

            async initializeAnimation() {
                if (this.useImages) {
                    await this.loadImageFrames(this.config);
                } else {
                    this.generateFrames();
                }
            }

            setupCanvas() {
                const resizeCanvas = () => {
                    this.canvas.width = window.innerWidth;
                    this.canvas.height = window.innerHeight;
                    this.redraw();
                };
                
                window.addEventListener('resize', resizeCanvas);
                resizeCanvas();
            }

            async loadImageFrames(config) {
                const loading = document.getElementById('loading');
                const loadingText = document.getElementById('loadingText');
                const loadStatus = document.getElementById('loadStatus');
                
                loadStatus.textContent = '加载图片中...';
                
                const loader = new ImageFrameLoader({
                    frameCount: this.totalFrames,
                    basePath: config.basePath || 'frames/',
                    fileExtension: config.fileExtension || '.jpg',
                    onProgress: (loaded, total) => {
                        const percent = Math.round((loaded / total) * 100);
                        loadingText.textContent = `正在加载图片帧... ${percent}%`;
                        document.getElementById('progressFill').style.width = percent + '%';
                    },
                    onComplete: (images) => {
                        this.frames = images;
                        loading.classList.add('hidden');
                        loadStatus.textContent = '图片模式';
                        document.getElementById('frameCount').textContent = this.totalFrames;
                        this.drawFrame(0);
                    },
                    onError: (error) => {
                        console.error('Frame loading failed:', error);
                        this.showError('图片加载失败，切换到程序生成模式');
                        this.useImages = false;
                        this.generateFrames();
                    }
                });

                await loader.loadAllFrames();
            }

            generateFrames() {
                const loading = document.getElementById('loading');
                const loadingText = document.getElementById('loadingText');
                const loadStatus = document.getElementById('loadStatus');
                
                loadingText.textContent = '正在生成动画帧...';
                loadStatus.textContent = '生成模式';
                
                // 这里保持原有的帧生成逻辑
                for (let i = 0; i < this.totalFrames; i++) {
                    const progress = i / (this.totalFrames - 1);
                    const frame = this.generateFrame(progress);
                    this.frames.push(frame);
                    
                    // 更新进度
                    const percent = Math.round(((i + 1) / this.totalFrames) * 100);
                    document.getElementById('progressFill').style.width = percent + '%';
                }
                
                loading.classList.add('hidden');
                document.getElementById('frameCount').textContent = this.totalFrames;
                this.drawFrame(0);
            }

            // 保持原有的generateFrame方法
            generateFrame(progress) {
                const tempCanvas = document.createElement('canvas');
                tempCanvas.width = this.canvas.width;
                tempCanvas.height = this.canvas.height;
                const tempCtx = tempCanvas.getContext('2d');
                
                this.drawSunriseScene(tempCtx, progress);
                return tempCanvas;
            }

            // 保持原有的drawSunriseScene方法（简化版）
            drawSunriseScene(ctx, progress) {
                const width = ctx.canvas.width;
                const height = ctx.canvas.height;
                
                ctx.clearRect(0, 0, width, height);
                
                // 简化的天空渐变
                const skyGradient = ctx.createLinearGradient(0, 0, 0, height);
                skyGradient.addColorStop(0, `hsl(${240 - progress * 180}, 70%, ${20 + progress * 60}%)`);
                skyGradient.addColorStop(1, `hsl(${240 - progress * 180}, 50%, ${10 + progress * 40}%)`);
                
                ctx.fillStyle = skyGradient;
                ctx.fillRect(0, 0, width, height);
                
                // 简化的太阳
                if (progress > 0.1) {
                    const sunY = height - (progress * height * 0.8);
                    const sunSize = 30 + progress * 20;
                    
                    ctx.fillStyle = `hsl(${45}, 100%, ${60 + progress * 20}%)`;
                    ctx.beginPath();
                    ctx.arc(width / 2, sunY, sunSize, 0, Math.PI * 2);
                    ctx.fill();
                }
            }

            drawFrame(frameIndex) {
                if (frameIndex >= 0 && frameIndex < this.frames.length) {
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    if (this.useImages && this.frames[frameIndex]) {
                        // 绘制图片帧
                        const img = this.frames[frameIndex];
                        if (img && img.complete) {
                            this.drawImageScaled(img);
                        }
                    } else {
                        // 绘制生成的帧
                        this.ctx.drawImage(this.frames[frameIndex], 0, 0);
                    }
                    
                    this.currentFrameIndex = frameIndex;
                    document.getElementById('currentFrame').textContent = frameIndex + 1;
                }
            }

            drawImageScaled(img) {
                const canvasRatio = this.canvas.width / this.canvas.height;
                const imageRatio = img.width / img.height;
                
                let drawWidth, drawHeight, offsetX = 0, offsetY = 0;
                
                if (canvasRatio > imageRatio) {
                    drawHeight = this.canvas.height;
                    drawWidth = drawHeight * imageRatio;
                    offsetX = (this.canvas.width - drawWidth) / 2;
                } else {
                    drawWidth = this.canvas.width;
                    drawHeight = drawWidth / imageRatio;
                    offsetY = (this.canvas.height - drawHeight) / 2;
                }
                
                this.ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
            }

            redraw() {
                if (this.frames.length > 0) {
                    this.drawFrame(this.currentFrameIndex);
                }
            }

            bindEvents() {
                let ticking = false;
                
                const handleScroll = () => {
                    if (!ticking) {
                        requestAnimationFrame(() => {
                            const scrollTop = window.pageYOffset;
                            const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
                            const progress = Math.min(scrollTop / documentHeight, 1);
                            
                            const frameIndex = Math.floor(progress * (this.totalFrames - 1));
                            this.drawFrame(frameIndex);
                            
                            document.getElementById('scrollProgress').textContent = Math.round(progress * 100) + '%';
                            document.getElementById('progressFill').style.width = (progress * 100) + '%';
                            
                            ticking = false;
                        });
                        ticking = true;
                    }
                };
                
                window.addEventListener('scroll', handleScroll);
            }

            toggleAutoPlay() {
                if (this.isAutoPlaying) {
                    clearInterval(this.autoPlayInterval);
                    this.isAutoPlaying = false;
                    document.getElementById('autoPlayBtn').textContent = '自动播放';
                } else {
                    this.autoPlayInterval = setInterval(() => {
                        this.currentFrameIndex = (this.currentFrameIndex + 1) % this.totalFrames;
                        this.drawFrame(this.currentFrameIndex);
                    }, 50);
                    this.isAutoPlaying = true;
                    document.getElementById('autoPlayBtn').textContent = '停止播放';
                }
            }

            reset() {
                if (this.isAutoPlaying) {
                    this.toggleAutoPlay();
                }
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }

            showError(message) {
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.querySelector('div').textContent = message;
                errorDiv.style.display = 'block';
                setTimeout(() => {
                    errorDiv.style.display = 'none';
                }, 3000);
            }
        }

        // 全局变量和函数
        let sunriseAnimation;

        function resetAnimation() {
            sunriseAnimation.reset();
        }

        function toggleAutoPlay() {
            sunriseAnimation.toggleAutoPlay();
        }

        function switchMode() {
            const mode = document.getElementById('modeSelector').value;
            const useImages = mode === 'images';
            
            // 重新初始化动画
            sunriseAnimation = new SunriseFrameAnimation({
                useImages: useImages,
                frameCount: 120,
                basePath: 'frames/',
                fileExtension: '.jpg'
            });
        }

        function preloadFrames() {
            // 这里可以实现预加载逻辑
            alert('预加载功能开发中...');
        }

        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        // 初始化
        window.addEventListener('load', () => {
            sunriseAnimation = new SunriseFrameAnimation({
                useImages: true,
                frameCount: 120,
                basePath: 'frames/',
                fileExtension: '.jpg'
            });
        });
    </script>
</body>
</html>
