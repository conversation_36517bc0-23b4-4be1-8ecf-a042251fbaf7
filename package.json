{"name": "sunrise-frame-animation", "version": "1.0.0", "description": "高级日出帧动画项目 - 支持程序生成和图片帧两种模式", "main": "frame-processor.js", "scripts": {"process-frames": "node frame-processor.js", "generate-test": "node frame-processor.js --count 120", "process-custom": "node frame-processor.js --input ./input --output ./frames --count 120 --format jpg --quality 85", "serve": "python3 -m http.server 8000", "serve-node": "npx http-server -p 8000 -c-1", "help": "node frame-processor.js --help"}, "keywords": ["animation", "canvas", "scroll-driven", "frame-animation", "sunrise", "web-animation"], "author": "Your Name", "license": "MIT", "devDependencies": {"http-server": "^14.1.1"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/sunrise-frame-animation.git"}, "bugs": {"url": "https://github.com/yourusername/sunrise-frame-animation/issues"}, "homepage": "https://github.com/yourusername/sunrise-frame-animation#readme"}