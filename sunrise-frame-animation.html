<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日出帧动画 - 滚动驱动</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            overflow-x: hidden;
            background: #000;
        }

        /* 滚动容器 */
        .scroll-container {
            height: 600vh; /* 6倍视口高度 */
            position: relative;
        }

        /* Canvas容器 */
        .canvas-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            z-index: 1;
        }

        #sunriseCanvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        /* 加载指示器 */
        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 1.2rem;
            z-index: 3;
            text-align: center;
        }

        .loading.hidden {
            display: none;
        }

        /* 进度条 */
        .progress-bar {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            z-index: 3;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b35, #ffd700, #87ceeb);
            border-radius: 2px;
            width: 0%;
            transition: width 0.1s ease-out;
        }

        /* 控制面板 */
        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 3;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            color: white;
            font-size: 14px;
        }

        .controls button {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px 0;
            display: block;
            width: 100%;
        }

        .controls button:hover {
            background: #ff4500;
        }

        /* 内容区域 */
        .content {
            position: relative;
            z-index: 2;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin-top: 100vh;
            padding: 50px;
            min-height: 200vh;
            color: white;
        }

        .content h1 {
            font-size: 3rem;
            margin-bottom: 2rem;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .content p {
            font-size: 1.2rem;
            line-height: 1.8;
            max-width: 800px;
            margin: 0 auto 2rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #ffd700;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .controls {
                top: 10px;
                right: 10px;
                padding: 10px;
            }
            
            .content h1 {
                font-size: 2rem;
            }
            
            .content p {
                font-size: 1rem;
                padding: 0 20px;
            }
            
            .progress-bar {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="scroll-container">
        <!-- Canvas容器 -->
        <div class="canvas-container">
            <canvas id="sunriseCanvas"></canvas>
        </div>

        <!-- 加载指示器 -->
        <div class="loading" id="loading">
            <div>正在生成日出帧...</div>
            <div style="margin-top: 10px; font-size: 0.9rem;">请稍候</div>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <!-- 控制面板 -->
        <div class="controls">
            <div>帧数: <span id="frameCount">0</span></div>
            <div>当前帧: <span id="currentFrame">0</span></div>
            <div>滚动进度: <span id="scrollProgress">0%</span></div>
            <button onclick="resetAnimation()">重置动画</button>
            <button onclick="toggleAutoPlay()">自动播放</button>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <h1>日出帧动画</h1>
            <p>这是一个使用Canvas技术实现的高级日出帧动画。动画通过程序生成多个帧，每一帧都代表日出过程中的一个时刻。</p>
            <p>当你滚动页面时，动画会根据滚动位置精确地显示对应的帧，创造出流畅的视觉体验。</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🎨 程序生成帧</h3>
                    <p>使用Canvas API动态生成每一帧，包含太阳位置、天空颜色、云朵等元素的精确计算。</p>
                </div>
                
                <div class="feature-card">
                    <h3>📱 响应式设计</h3>
                    <p>自适应不同屏幕尺寸，在移动设备和桌面设备上都能提供最佳的视觉体验。</p>
                </div>
                
                <div class="feature-card">
                    <h3>⚡ 性能优化</h3>
                    <p>使用requestAnimationFrame和帧缓存技术，确保动画流畅运行，不影响页面性能。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🎮 交互控制</h3>
                    <p>提供多种控制选项，包括手动滚动、自动播放、重置等功能。</p>
                </div>
            </div>
            
            <p style="margin-top: 50px; text-align: center; font-size: 1.1rem;">
                继续滚动体验完整的日出之旅...
            </p>
        </div>
    </div>

    <script>
        class SunriseFrameAnimation {
            constructor() {
                this.canvas = document.getElementById('sunriseCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.frames = [];
                this.totalFrames = 120; // 总帧数
                this.currentFrameIndex = 0;
                this.isAutoPlaying = false;
                this.autoPlayInterval = null;
                
                this.setupCanvas();
                this.generateFrames();
                this.bindEvents();
            }

            setupCanvas() {
                const resizeCanvas = () => {
                    this.canvas.width = window.innerWidth;
                    this.canvas.height = window.innerHeight;
                    this.redraw();
                };
                
                window.addEventListener('resize', resizeCanvas);
                resizeCanvas();
            }

            generateFrames() {
                const loading = document.getElementById('loading');
                
                for (let i = 0; i < this.totalFrames; i++) {
                    const progress = i / (this.totalFrames - 1);
                    const frame = this.generateFrame(progress);
                    this.frames.push(frame);
                }
                
                loading.classList.add('hidden');
                document.getElementById('frameCount').textContent = this.totalFrames;
                this.drawFrame(0);
            }

            generateFrame(progress) {
                const tempCanvas = document.createElement('canvas');
                tempCanvas.width = this.canvas.width;
                tempCanvas.height = this.canvas.height;
                const tempCtx = tempCanvas.getContext('2d');
                
                this.drawSunriseScene(tempCtx, progress);
                
                return tempCanvas;
            }

            drawSunriseScene(ctx, progress) {
                const width = ctx.canvas.width;
                const height = ctx.canvas.height;
                
                // 清空画布
                ctx.clearRect(0, 0, width, height);
                
                // 计算动画参数
                const sunY = height - (progress * height * 0.8);
                const sunSize = 60 + progress * 40;
                
                // 天空渐变
                const skyGradient = ctx.createLinearGradient(0, 0, 0, height);
                
                if (progress < 0.2) {
                    // 深夜
                    skyGradient.addColorStop(0, '#0a0a2e');
                    skyGradient.addColorStop(0.5, '#16213e');
                    skyGradient.addColorStop(1, '#1a1a3a');
                } else if (progress < 0.4) {
                    // 黎明
                    const factor = (progress - 0.2) / 0.2;
                    skyGradient.addColorStop(0, this.lerpColor('#0a0a2e', '#1a1a3a', factor));
                    skyGradient.addColorStop(0.5, this.lerpColor('#16213e', '#2d4a5a', factor));
                    skyGradient.addColorStop(1, this.lerpColor('#1a1a3a', '#4a5d6a', factor));
                } else if (progress < 0.6) {
                    // 日出
                    const factor = (progress - 0.4) / 0.2;
                    skyGradient.addColorStop(0, this.lerpColor('#1a1a3a', '#ff6b35', factor));
                    skyGradient.addColorStop(0.5, this.lerpColor('#2d4a5a', '#f7931e', factor));
                    skyGradient.addColorStop(1, this.lerpColor('#4a5d6a', '#ffcc5c', factor));
                } else if (progress < 0.8) {
                    // 上午
                    const factor = (progress - 0.6) / 0.2;
                    skyGradient.addColorStop(0, this.lerpColor('#ff6b35', '#87ceeb', factor));
                    skyGradient.addColorStop(0.5, this.lerpColor('#f7931e', '#98d8e8', factor));
                    skyGradient.addColorStop(1, this.lerpColor('#ffcc5c', '#b8e6f0', factor));
                } else {
                    // 白天
                    skyGradient.addColorStop(0, '#87ceeb');
                    skyGradient.addColorStop(0.5, '#b0e0e6');
                    skyGradient.addColorStop(1, '#e0f6ff');
                }
                
                // 绘制天空
                ctx.fillStyle = skyGradient;
                ctx.fillRect(0, 0, width, height);
                
                // 绘制云朵
                this.drawClouds(ctx, progress, width, height);
                
                // 绘制太阳
                if (progress > 0.1) {
                    this.drawSun(ctx, width / 2, sunY, sunSize, progress);
                }
                
                // 绘制地面
                this.drawGround(ctx, progress, width, height);
                
                // 绘制星星（仅在夜晚）
                if (progress < 0.3) {
                    this.drawStars(ctx, progress, width, height);
                }
            }

            drawSun(ctx, x, y, size, progress) {
                const sunGradient = ctx.createRadialGradient(x, y, 0, x, y, size);
                
                if (progress < 0.5) {
                    sunGradient.addColorStop(0, '#ffb347');
                    sunGradient.addColorStop(1, '#ff8c42');
                } else {
                    sunGradient.addColorStop(0, '#ffd700');
                    sunGradient.addColorStop(1, '#ffaa00');
                }
                
                // 太阳光晕
                ctx.shadowColor = progress < 0.5 ? '#ff8c42' : '#ffd700';
                ctx.shadowBlur = 30 + progress * 20;
                
                ctx.fillStyle = sunGradient;
                ctx.beginPath();
                ctx.arc(x, y, size / 2, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.shadowBlur = 0;
            }

            drawClouds(ctx, progress, width, height) {
                const cloudOpacity = Math.min(progress * 2, 0.8);
                ctx.globalAlpha = cloudOpacity;
                
                // 云朵1
                this.drawCloud(ctx, width * 0.2, height * 0.2, 80, 30);
                // 云朵2
                this.drawCloud(ctx, width * 0.7, height * 0.15, 100, 35);
                // 云朵3
                this.drawCloud(ctx, width * 0.5, height * 0.25, 90, 32);
                
                ctx.globalAlpha = 1;
            }

            drawCloud(ctx, x, y, width, height) {
                ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                ctx.beginPath();
                ctx.ellipse(x, y, width / 2, height / 2, 0, 0, Math.PI * 2);
                ctx.fill();
                
                // 添加云朵的细节
                ctx.beginPath();
                ctx.ellipse(x - width * 0.3, y, width * 0.4, height * 0.6, 0, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.beginPath();
                ctx.ellipse(x + width * 0.3, y, width * 0.4, height * 0.6, 0, 0, Math.PI * 2);
                ctx.fill();
            }

            drawGround(ctx, progress, width, height) {
                const groundHeight = height * 0.3;
                const groundGradient = ctx.createLinearGradient(0, height - groundHeight, 0, height);
                
                if (progress < 0.5) {
                    groundGradient.addColorStop(0, '#2d4a22');
                    groundGradient.addColorStop(1, '#1a2e15');
                } else {
                    groundGradient.addColorStop(0, '#6b8e5a');
                    groundGradient.addColorStop(1, '#4a6741');
                }
                
                ctx.fillStyle = groundGradient;
                ctx.fillRect(0, height - groundHeight, width, groundHeight);
            }

            drawStars(ctx, progress, width, height) {
                const starOpacity = Math.max(0, 1 - progress * 5);
                ctx.globalAlpha = starOpacity;
                ctx.fillStyle = '#ffffff';
                
                // 绘制一些星星
                const stars = [
                    { x: width * 0.1, y: height * 0.1 },
                    { x: width * 0.3, y: height * 0.05 },
                    { x: width * 0.8, y: height * 0.08 },
                    { x: width * 0.9, y: height * 0.15 },
                    { x: width * 0.6, y: height * 0.12 }
                ];
                
                stars.forEach(star => {
                    ctx.beginPath();
                    ctx.arc(star.x, star.y, 2, 0, Math.PI * 2);
                    ctx.fill();
                });
                
                ctx.globalAlpha = 1;
            }

            lerpColor(color1, color2, factor) {
                // 简化的颜色插值
                return factor < 0.5 ? color1 : color2;
            }

            drawFrame(frameIndex) {
                if (frameIndex >= 0 && frameIndex < this.frames.length) {
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    this.ctx.drawImage(this.frames[frameIndex], 0, 0);
                    this.currentFrameIndex = frameIndex;
                    document.getElementById('currentFrame').textContent = frameIndex + 1;
                }
            }

            redraw() {
                if (this.frames.length > 0) {
                    this.drawFrame(this.currentFrameIndex);
                }
            }

            bindEvents() {
                let ticking = false;
                
                const handleScroll = () => {
                    if (!ticking) {
                        requestAnimationFrame(() => {
                            const scrollTop = window.pageYOffset;
                            const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
                            const progress = Math.min(scrollTop / documentHeight, 1);
                            
                            const frameIndex = Math.floor(progress * (this.totalFrames - 1));
                            this.drawFrame(frameIndex);
                            
                            // 更新UI
                            document.getElementById('scrollProgress').textContent = Math.round(progress * 100) + '%';
                            document.getElementById('progressFill').style.width = (progress * 100) + '%';
                            
                            ticking = false;
                        });
                        ticking = true;
                    }
                };
                
                window.addEventListener('scroll', handleScroll);
            }

            toggleAutoPlay() {
                if (this.isAutoPlaying) {
                    clearInterval(this.autoPlayInterval);
                    this.isAutoPlaying = false;
                } else {
                    this.autoPlayInterval = setInterval(() => {
                        this.currentFrameIndex = (this.currentFrameIndex + 1) % this.totalFrames;
                        this.drawFrame(this.currentFrameIndex);
                    }, 50);
                    this.isAutoPlaying = true;
                }
            }

            reset() {
                if (this.isAutoPlaying) {
                    this.toggleAutoPlay();
                }
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }

        // 全局函数
        let sunriseAnimation;

        function resetAnimation() {
            sunriseAnimation.reset();
        }

        function toggleAutoPlay() {
            sunriseAnimation.toggleAutoPlay();
        }

        // 初始化
        window.addEventListener('load', () => {
            sunriseAnimation = new SunriseFrameAnimation();
        });
    </script>
</body>
</html>
