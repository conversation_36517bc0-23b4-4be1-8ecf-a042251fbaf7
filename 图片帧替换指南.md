# 图片帧替换实施指南

## 概述

本指南详细说明如何将当前程序生成的日出动画帧替换为实际的图片帧，包括代码重构、性能优化和最佳实践。

## 1. 图片准备阶段

### 1.1 图片命名规范
```
frames/
├── frame_001.jpg
├── frame_002.jpg
├── frame_003.jpg
...
├── frame_120.jpg
```

### 1.2 图片格式建议
- **JPG**: 适合复杂场景，文件小
- **PNG**: 需要透明度时使用
- **WebP**: 现代浏览器，最佳压缩比

### 1.3 图片尺寸规范
```javascript
// 建议的图片尺寸配置
const IMAGE_CONFIGS = {
    desktop: { width: 1920, height: 1080 },
    tablet: { width: 1024, height: 768 },
    mobile: { width: 750, height: 1334 }
};
```

## 2. 核心代码重构

### 2.1 图片加载器类
```javascript
class ImageFrameLoader {
    constructor(config) {
        this.frameCount = config.frameCount || 120;
        this.basePath = config.basePath || 'frames/';
        this.fileExtension = config.fileExtension || '.jpg';
        this.images = [];
        this.loadedCount = 0;
        this.onProgress = config.onProgress || (() => {});
        this.onComplete = config.onComplete || (() => {});
        this.onError = config.onError || (() => {});
    }

    async loadAllFrames() {
        const promises = [];
        
        for (let i = 1; i <= this.frameCount; i++) {
            const frameNumber = i.toString().padStart(3, '0');
            const imagePath = `${this.basePath}frame_${frameNumber}${this.fileExtension}`;
            promises.push(this.loadSingleFrame(imagePath, i - 1));
        }

        try {
            await Promise.all(promises);
            this.onComplete(this.images);
        } catch (error) {
            this.onError(error);
        }
    }

    loadSingleFrame(imagePath, index) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            
            img.onload = () => {
                this.images[index] = img;
                this.loadedCount++;
                this.onProgress(this.loadedCount, this.frameCount);
                resolve(img);
            };
            
            img.onerror = () => {
                console.error(`Failed to load frame: ${imagePath}`);
                reject(new Error(`Failed to load ${imagePath}`));
            };
            
            img.src = imagePath;
        });
    }

    // 渐进式加载：先加载关键帧
    async loadKeyFrames() {
        const keyFrameIndices = [1, 30, 60, 90, 120]; // 关键帧
        const promises = keyFrameIndices.map(i => {
            const frameNumber = i.toString().padStart(3, '0');
            const imagePath = `${this.basePath}frame_${frameNumber}${this.fileExtension}`;
            return this.loadSingleFrame(imagePath, i - 1);
        });

        await Promise.all(promises);
        
        // 然后加载其余帧
        return this.loadRemainingFrames(keyFrameIndices);
    }
}
```

### 2.2 修改主动画类
```javascript
class SunriseFrameAnimation {
    constructor(config = {}) {
        this.canvas = document.getElementById('sunriseCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.frames = [];
        this.totalFrames = config.frameCount || 120;
        this.currentFrameIndex = 0;
        this.isAutoPlaying = false;
        this.autoPlayInterval = null;
        this.useImages = config.useImages || false;
        
        this.setupCanvas();
        
        if (this.useImages) {
            this.loadImageFrames(config);
        } else {
            this.generateFrames(); // 保持原有功能
        }
        
        this.bindEvents();
    }

    async loadImageFrames(config) {
        const loading = document.getElementById('loading');
        const progressText = loading.querySelector('div');
        
        const loader = new ImageFrameLoader({
            frameCount: this.totalFrames,
            basePath: config.basePath || 'frames/',
            fileExtension: config.fileExtension || '.jpg',
            onProgress: (loaded, total) => {
                const percent = Math.round((loaded / total) * 100);
                progressText.textContent = `正在加载图片帧... ${percent}%`;
                document.getElementById('progressFill').style.width = percent + '%';
            },
            onComplete: (images) => {
                this.frames = images;
                loading.classList.add('hidden');
                document.getElementById('frameCount').textContent = this.totalFrames;
                this.drawFrame(0);
            },
            onError: (error) => {
                console.error('Frame loading failed:', error);
                // 回退到程序生成
                this.useImages = false;
                this.generateFrames();
            }
        });

        await loader.loadAllFrames();
    }

    drawFrame(frameIndex) {
        if (frameIndex >= 0 && frameIndex < this.frames.length) {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            
            if (this.useImages) {
                // 绘制图片帧
                const img = this.frames[frameIndex];
                if (img && img.complete) {
                    // 保持宽高比的缩放绘制
                    this.drawImageScaled(img);
                }
            } else {
                // 原有的Canvas绘制方式
                this.ctx.drawImage(this.frames[frameIndex], 0, 0);
            }
            
            this.currentFrameIndex = frameIndex;
            document.getElementById('currentFrame').textContent = frameIndex + 1;
        }
    }

    drawImageScaled(img) {
        const canvasRatio = this.canvas.width / this.canvas.height;
        const imageRatio = img.width / img.height;
        
        let drawWidth, drawHeight, offsetX = 0, offsetY = 0;
        
        if (canvasRatio > imageRatio) {
            // Canvas更宽，以高度为准
            drawHeight = this.canvas.height;
            drawWidth = drawHeight * imageRatio;
            offsetX = (this.canvas.width - drawWidth) / 2;
        } else {
            // Canvas更高，以宽度为准
            drawWidth = this.canvas.width;
            drawHeight = drawWidth / imageRatio;
            offsetY = (this.canvas.height - drawHeight) / 2;
        }
        
        this.ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
    }
}
```

## 3. 性能优化策略

### 3.1 内存管理
```javascript
class MemoryOptimizedFrameLoader {
    constructor(config) {
        this.maxCachedFrames = config.maxCachedFrames || 20;
        this.frameCache = new Map();
        this.loadQueue = [];
    }

    // LRU缓存策略
    cacheFrame(index, image) {
        if (this.frameCache.size >= this.maxCachedFrames) {
            const firstKey = this.frameCache.keys().next().value;
            this.frameCache.delete(firstKey);
        }
        this.frameCache.set(index, image);
    }

    // 预测性加载
    preloadNearbyFrames(currentIndex, range = 5) {
        for (let i = Math.max(0, currentIndex - range); 
             i <= Math.min(this.totalFrames - 1, currentIndex + range); 
             i++) {
            if (!this.frameCache.has(i)) {
                this.loadQueue.push(i);
            }
        }
        this.processLoadQueue();
    }
}
```

### 3.2 响应式图片支持
```javascript
class ResponsiveImageLoader {
    constructor() {
        this.devicePixelRatio = window.devicePixelRatio || 1;
        this.screenWidth = window.innerWidth;
    }

    getOptimalImagePath(frameIndex) {
        const frameNumber = frameIndex.toString().padStart(3, '0');
        
        if (this.screenWidth <= 768) {
            return `frames/mobile/frame_${frameNumber}.jpg`;
        } else if (this.screenWidth <= 1024) {
            return `frames/tablet/frame_${frameNumber}.jpg`;
        } else {
            return `frames/desktop/frame_${frameNumber}.jpg`;
        }
    }
}
```

## 4. 实施步骤

### 步骤1: 准备图片资源
1. 导出动画帧（120帧）
2. 优化图片大小和质量
3. 按命名规范组织文件

### 步骤2: 更新HTML配置
```html
<script>
window.addEventListener('load', () => {
    sunriseAnimation = new SunriseFrameAnimation({
        useImages: true,
        frameCount: 120,
        basePath: 'frames/',
        fileExtension: '.jpg'
    });
});
</script>
```

### 步骤3: 测试和优化
1. 测试加载性能
2. 检查内存使用
3. 验证不同设备兼容性

## 5. 最佳实践

### 5.1 图片优化
- 使用适当的压缩比（JPG质量80-90%）
- 考虑使用WebP格式（现代浏览器）
- 为不同设备准备不同尺寸

### 5.2 加载策略
- 优先加载关键帧
- 实现渐进式加载
- 添加加载失败回退机制

### 5.3 用户体验
- 显示详细的加载进度
- 提供加载失败提示
- 支持离线缓存

## 6. 错误处理和回退

```javascript
// 混合模式：图片优先，程序生成作为回退
class HybridFrameAnimation extends SunriseFrameAnimation {
    constructor(config) {
        super(config);
        this.fallbackToGenerated = true;
    }

    async loadImageFrames(config) {
        try {
            await super.loadImageFrames(config);
        } catch (error) {
            if (this.fallbackToGenerated) {
                console.warn('Image loading failed, falling back to generated frames');
                this.useImages = false;
                this.generateFrames();
            }
        }
    }
}
```

## 7. 快速测试工具

为了帮助您快速测试图片帧功能，我提供了一个简单的图片生成脚本：

```javascript
// 在浏览器控制台中运行此代码来生成测试图片
function generateTestFrames() {
    const canvas = document.createElement('canvas');
    canvas.width = 800;
    canvas.height = 600;
    const ctx = canvas.getContext('2d');

    for (let i = 0; i < 120; i++) {
        const progress = i / 119;

        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 绘制简单的测试图案
        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(0, `hsl(${240 - progress * 180}, 70%, ${20 + progress * 60}%)`);
        gradient.addColorStop(1, `hsl(${240 - progress * 180}, 50%, ${10 + progress * 40}%)`);

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 添加帧号标识
        ctx.fillStyle = 'white';
        ctx.font = '24px Arial';
        ctx.fillText(`Frame ${i + 1}`, 20, 40);

        // 下载图片
        canvas.toBlob(blob => {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `frame_${(i + 1).toString().padStart(3, '0')}.jpg`;
            a.click();
            URL.revokeObjectURL(url);
        }, 'image/jpeg', 0.8);
    }
}
```

## 8. 目录结构示例

```
项目根目录/
├── sunrise-image-frames.html
├── frames/
│   ├── desktop/
│   │   ├── frame_001.jpg
│   │   ├── frame_002.jpg
│   │   └── ...
│   ├── tablet/
│   │   ├── frame_001.jpg
│   │   ├── frame_002.jpg
│   │   └── ...
│   └── mobile/
│       ├── frame_001.jpg
│       ├── frame_002.jpg
│       └── ...
└── 图片帧替换指南.md
```

## 9. 性能监控

```javascript
// 添加性能监控
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            loadTime: 0,
            memoryUsage: 0,
            frameRate: 0
        };
    }

    startLoadTimer() {
        this.loadStartTime = performance.now();
    }

    endLoadTimer() {
        this.metrics.loadTime = performance.now() - this.loadStartTime;
        console.log(`Frames loaded in ${this.metrics.loadTime}ms`);
    }

    checkMemoryUsage() {
        if (performance.memory) {
            this.metrics.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
            console.log(`Memory usage: ${this.metrics.memoryUsage.toFixed(2)}MB`);
        }
    }
}
```

这个方案提供了完整的图片帧替换解决方案，既保持了原有功能的兼容性，又添加了强大的图片加载和管理功能。通过智能回退机制，确保在任何情况下都能为用户提供良好的体验。
